#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
独立图层处理器
实现每个图层单独处理的流程：
1. 对墙体进行迭代合并
2. 对其他图层每个图层单独进行简单去重处理
3. 数据输出
"""

import copy
import time
from typing import List, Dict, Any, Tuple
from collections import defaultdict, OrderedDict

try:
    from line_merger import SimpleLineMerger
    LINE_MERGER_AVAILABLE = True
except ImportError:
    LINE_MERGER_AVAILABLE = False
    print("⚠️ 原始线条合并器不可用")

try:
    from optimized_line_merger import FastLineMerger
    FAST_MERGER_AVAILABLE = True
except ImportError:
    FAST_MERGER_AVAILABLE = False
    print("⚠️ 快速线条合并器不可用")

try:
    from simple_fast_merger import SimpleFastMerger
    SIMPLE_FAST_MERGER_AVAILABLE = True
except ImportError:
    SIMPLE_FAST_MERGER_AVAILABLE = False
    print("⚠️ 简单快速合并器不可用")

try:
    from optimized_deduplication import HybridDeduplicator
    OPTIMIZED_DEDUP_AVAILABLE = True
except ImportError:
    OPTIMIZED_DEDUP_AVAILABLE = False
    print("⚠️ 优化去重器不可用")

try:
    from ultra_fast_deduplication import HybridUltraFastDeduplicator
    ULTRA_FAST_DEDUP_AVAILABLE = True
except ImportError:
    ULTRA_FAST_DEDUP_AVAILABLE = False
    print("⚠️ 超快速去重器不可用")


class IndependentLayerProcessor:
    """
    独立图层处理器
    
    核心特点：
    1. 每个图层完全独立处理
    2. 墙体图层：迭代合并
    3. 其他图层：简单去重
    4. 保持图层处理顺序
    5. 详细的处理追踪
    """
    
    def __init__(self, distance_threshold=5, angle_threshold=2):
        """
        初始化独立图层处理器
        
        Args:
            distance_threshold: 距离阈值
            angle_threshold: 角度阈值
        """
        self.distance_threshold = distance_threshold
        self.angle_threshold = angle_threshold
        
        # 墙体图层识别模式
        self.wall_layer_patterns = [
            'wall', '墙', 'a-wall', 'arch-wall', 'a-wall-', 'wall-'
        ]
        
        # 处理统计
        self.processing_stats = {
            'total_entities_input': 0,
            'total_entities_output': 0,
            'total_layers_processed': 0,
            'wall_layers_count': 0,
            'other_layers_count': 0,
            'processing_time': 0.0,
            'layer_processing_order': [],
            'layer_details': OrderedDict()
        }

        # 初始化超快速去重器
        if ULTRA_FAST_DEDUP_AVAILABLE:
            self.deduplicator = HybridUltraFastDeduplicator(threshold=30)
            print("⚡ 超快速去重器初始化完成")
        elif OPTIMIZED_DEDUP_AVAILABLE:
            self.deduplicator = HybridDeduplicator(threshold=50)
            print("🚀 优化去重器初始化完成")
        else:
            self.deduplicator = None
            print("⚠️ 使用传统去重方式")

        # 初始化快速线条合并器
        if SIMPLE_FAST_MERGER_AVAILABLE:
            self.fast_merger = SimpleFastMerger(
                distance_threshold=distance_threshold,
                angle_threshold=angle_threshold,
                max_iterations=3
            )
            print("⚡ 简单快速线条合并器初始化完成")
        elif FAST_MERGER_AVAILABLE:
            self.fast_merger = FastLineMerger(
                distance_threshold=distance_threshold,
                angle_threshold=angle_threshold,
                max_iterations=3
            )
            print("⚡ 快速线条合并器初始化完成")
        else:
            self.fast_merger = None
            print("⚠️ 使用传统线条合并方式")
        
        print("🎯 独立图层处理器初始化完成")
    
    def process_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理实体的主要方法 - 每个图层独立处理
        
        Args:
            entities: 输入实体列表
            
        Returns:
            处理后的实体列表
        """
        print(f"🔄 开始独立图层处理: {len(entities)} 个实体")
        start_time = time.time()
        
        # 重置统计
        self.processing_stats = {
            'total_entities_input': len(entities),
            'total_entities_output': 0,
            'total_layers_processed': 0,
            'wall_layers_count': 0,
            'other_layers_count': 0,
            'processing_time': 0.0,
            'layer_processing_order': [],
            'layer_details': OrderedDict()
        }
        
        # 第一步：按图层分组
        layer_groups = self._group_entities_by_layer(entities)
        
        print(f"   📋 发现 {len(layer_groups)} 个图层")
        self.processing_stats['total_layers_processed'] = len(layer_groups)
        
        # 第二步：分类图层
        wall_layers, other_layers = self._classify_layers(layer_groups)
        
        print(f"   🏗️ 墙体图层: {len(wall_layers)} 个")
        print(f"   📄 其他图层: {len(other_layers)} 个")
        
        # 第三步：独立处理每个图层
        all_processed_entities = []
        
        # 3.1 处理墙体图层
        for layer_name in wall_layers:
            layer_entities = layer_groups[layer_name]
            processed_entities = self._process_single_wall_layer(layer_name, layer_entities)
            all_processed_entities.extend(processed_entities)
        
        # 3.2 处理其他图层
        for layer_name in other_layers:
            layer_entities = layer_groups[layer_name]
            processed_entities = self._process_single_other_layer(layer_name, layer_entities)
            all_processed_entities.extend(processed_entities)
        
        # 第四步：数据输出
        final_output = self._prepare_final_output(all_processed_entities)
        
        # 完成统计
        self.processing_stats['total_entities_output'] = len(final_output)
        self.processing_stats['processing_time'] = time.time() - start_time
        
        print(f"✅ 独立图层处理完成: {len(entities)} -> {len(final_output)} 个实体")
        print(f"   处理时间: {self.processing_stats['processing_time']:.3f} 秒")
        print(f"   处理图层: {self.processing_stats['total_layers_processed']} 个")
        
        return final_output
    
    def _group_entities_by_layer(self, entities: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按图层分组实体"""
        layer_groups = defaultdict(list)
        
        for entity in entities:
            if isinstance(entity, dict):
                layer = entity.get('layer', 'UNKNOWN')
                # 创建实体副本，确保原始数据不被修改
                entity_copy = copy.deepcopy(entity)
                entity_copy['original_layer'] = layer
                entity_copy['processing_stage'] = 'grouped'
                layer_groups[layer].append(entity_copy)
        
        return dict(layer_groups)
    
    def _classify_layers(self, layer_groups: Dict[str, List[Dict[str, Any]]]) -> Tuple[List[str], List[str]]:
        """分类图层：墙体图层和其他图层"""
        wall_layers = []
        other_layers = []
        
        for layer_name in layer_groups.keys():
            if self._is_wall_layer(layer_name):
                wall_layers.append(layer_name)
                self.processing_stats['wall_layers_count'] += 1
            else:
                other_layers.append(layer_name)
                self.processing_stats['other_layers_count'] += 1
        
        return wall_layers, other_layers
    
    def _is_wall_layer(self, layer_name: str) -> bool:
        """判断是否为墙体图层"""
        layer_name_lower = layer_name.lower()
        return any(pattern in layer_name_lower for pattern in self.wall_layer_patterns)
    
    def _process_single_wall_layer(self, layer_name: str, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理单个墙体图层：迭代合并"""
        print(f"   🏗️ 处理墙体图层: {layer_name} ({len(entities)} 个实体)")
        
        start_time = time.time()
        
        # 分离线条和非线条实体
        line_entities = []
        non_line_entities = []
        
        for entity in entities:
            entity['processing_stage'] = 'wall_layer_processing'
            if self._is_line_entity(entity):
                line_entities.append(entity)
            else:
                non_line_entities.append(entity)
        
        print(f"     📏 线条实体: {len(line_entities)} 个")
        print(f"     📄 非线条实体: {len(non_line_entities)} 个")
        
        # 对线条实体进行迭代合并
        if line_entities:
            merged_lines = self._iterative_merge_lines(line_entities, layer_name)
        else:
            merged_lines = line_entities
            print(f"     ⚠️ 跳过线条合并（无线条实体）")
        
        # 对非线条实体进行优化去重
        deduplicated_non_lines = self._optimized_deduplicate_entities(non_line_entities, layer_name, 'wall_non_line')
        
        # 合并结果
        layer_result = merged_lines + deduplicated_non_lines
        
        # 添加图层处理标记
        for entity in layer_result:
            entity['layer_processing_type'] = 'wall_iterative_merge'
            entity['processed_layer'] = layer_name
            entity['processing_stage'] = 'wall_layer_completed'
        
        processing_time = time.time() - start_time
        
        # 记录图层处理详情
        self.processing_stats['layer_processing_order'].append(layer_name)
        self.processing_stats['layer_details'][layer_name] = {
            'layer_type': 'wall',
            'input_count': len(entities),
            'output_count': len(layer_result),
            'line_entities': len(line_entities),
            'non_line_entities': len(non_line_entities),
            'merged_lines': len(merged_lines),
            'deduplicated_non_lines': len(deduplicated_non_lines),
            'processing_time': processing_time,
            'reduction_count': len(entities) - len(layer_result),
            'processing_method': 'iterative_merge + simple_dedup'
        }
        
        print(f"     ✅ 墙体图层处理完成: {len(entities)} -> {len(layer_result)} 个实体")
        print(f"     🕒 处理时间: {processing_time:.3f} 秒")
        
        return layer_result
    
    def _process_single_other_layer(self, layer_name: str, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理单个其他图层：简单去重"""
        print(f"   📄 处理其他图层: {layer_name} ({len(entities)} 个实体)")
        
        start_time = time.time()
        
        # 标记处理阶段
        for entity in entities:
            entity['processing_stage'] = 'other_layer_processing'
        
        # 对所有实体进行优化去重
        deduplicated_entities = self._optimized_deduplicate_entities(entities, layer_name, 'other_layer')
        
        # 添加图层处理标记
        for entity in deduplicated_entities:
            entity['layer_processing_type'] = 'simple_deduplication'
            entity['processed_layer'] = layer_name
            entity['processing_stage'] = 'other_layer_completed'
        
        processing_time = time.time() - start_time
        
        # 记录图层处理详情
        self.processing_stats['layer_processing_order'].append(layer_name)
        self.processing_stats['layer_details'][layer_name] = {
            'layer_type': 'other',
            'input_count': len(entities),
            'output_count': len(deduplicated_entities),
            'processing_time': processing_time,
            'reduction_count': len(entities) - len(deduplicated_entities),
            'processing_method': 'simple_deduplication'
        }
        
        print(f"     ✅ 其他图层处理完成: {len(entities)} -> {len(deduplicated_entities)} 个实体")
        print(f"     🕒 处理时间: {processing_time:.3f} 秒")
        
        return deduplicated_entities
    
    def _iterative_merge_lines(self, line_entities: List[Dict[str, Any]], layer_name: str) -> List[Dict[str, Any]]:
        """迭代合并线条实体（优化版）"""
        if not line_entities:
            return []

        print(f"       🔄 开始迭代合并线条...")

        # 提取线条坐标
        line_coords = []
        entity_map = {}

        for i, entity in enumerate(line_entities):
            coords = self._extract_line_coordinates(entity)
            if coords:
                line_coords.append(coords)
                entity_map[i] = entity

        if not line_coords:
            return line_entities

        # 优先使用快速合并器
        if self.fast_merger:
            print(f"         ⚡ 使用快速线条合并器...")
            try:
                merged_coords = self.fast_merger.merge_lines(line_coords)
            except Exception as e:
                print(f"         ❌ 快速合并失败: {e}")
                merged_coords = self._fallback_merge(line_coords)
        elif LINE_MERGER_AVAILABLE:
            print(f"         🔄 使用传统线条合并器...")
            merger = SimpleLineMerger(
                distance_threshold=self.distance_threshold,
                angle_threshold=self.angle_threshold
            )
            try:
                merged_coords = merger.merge_lines(line_coords)
            except Exception as e:
                print(f"         ❌ 传统合并失败: {e}")
                merged_coords = line_coords
        else:
            print(f"         ⚠️ 无可用合并器，跳过合并")
            merged_coords = line_coords

        # 重建实体
        merged_entities = self._rebuild_merged_entities(merged_coords, line_entities, layer_name)

        print(f"         ✅ 迭代合并完成: {len(line_entities)} -> {len(merged_entities)} 线条")
        return merged_entities

    def _fallback_merge(self, line_coords: List[List[List[float]]]) -> List[List[List[float]]]:
        """回退合并方法"""
        print(f"         🔄 使用回退合并方法...")
        # 简单的回退：不进行合并，直接返回原始坐标
        return line_coords

    def _rebuild_merged_entities(self, merged_coords: List[List[List[float]]],
                               original_entities: List[Dict[str, Any]],
                               layer_name: str) -> List[Dict[str, Any]]:
        """重建合并后的实体"""
        merged_entities = []

        for i, coords_obj in enumerate(merged_coords):
            # 处理不同类型的坐标对象
            if hasattr(coords_obj, 'coords'):
                coords = list(coords_obj.coords)
            elif isinstance(coords_obj, (list, tuple)):
                coords = coords_obj
            else:
                try:
                    coords = list(coords_obj)
                except:
                    print(f"         ⚠️ 无法处理坐标对象类型: {type(coords_obj)}")
                    continue

            if len(coords) < 2:
                continue

            # 创建新的合并实体
            merged_entity = {
                'type': 'LINE',
                'layer': layer_name,
                'original_layer': layer_name,
                'points': coords,
                'start_point': coords[0],
                'end_point': coords[-1],
                'merged_from_count': len(original_entities),
                'merged_by': 'IndependentLayerProcessor',
                'processing_type': 'iterative_merge',
                'merge_timestamp': time.time(),
                'processing_stage': 'line_merged'
            }

            # 继承第一个实体的其他属性
            if original_entities:
                first_entity = original_entities[0]
                for key in ['color', 'linetype', 'lineweight']:
                    if key in first_entity:
                        merged_entity[key] = first_entity[key]

            merged_entities.append(merged_entity)

        return merged_entities

    def _optimized_deduplicate_entities(self, entities: List[Dict[str, Any]],
                                      layer_name: str, context: str) -> List[Dict[str, Any]]:
        """优化去重实体（使用高效算法）"""
        if not entities:
            return []

        if self.deduplicator:
            # 使用优化去重器
            return self.deduplicator.deduplicate_entities(entities, layer_name, context)
        else:
            # 回退到简单去重
            print(f"       ⚠️ 回退到简单去重...")
            return self._simple_deduplicate_entities(entities, layer_name, context)

    def _simple_deduplicate_entities(self, entities: List[Dict[str, Any]],
                                   layer_name: str, context: str) -> List[Dict[str, Any]]:
        """简单去重实体"""
        if not entities:
            return []
        
        print(f"       🔍 开始简单去重处理 ({context})...")
        
        deduplicated = []
        seen_entities = []
        
        for entity in entities:
            is_duplicate = False
            
            # 简单比较：检查是否与已有实体相同
            for seen_entity in seen_entities:
                if self._is_simple_duplicate(entity, seen_entity):
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                # 添加去重标记
                entity_copy = copy.deepcopy(entity)
                entity_copy['processed_by'] = 'IndependentLayerProcessor'
                entity_copy['processing_type'] = 'simple_deduplication'
                entity_copy['dedup_timestamp'] = time.time()
                entity_copy['dedup_context'] = context
                entity_copy['processing_stage'] = 'deduplicated'
                
                deduplicated.append(entity_copy)
                seen_entities.append(entity)
        
        removed_count = len(entities) - len(deduplicated)
        if removed_count > 0:
            print(f"         🗑️ 简单去重移除实体: {removed_count} 个")
        
        return deduplicated

    def _is_simple_duplicate(self, entity1: Dict[str, Any], entity2: Dict[str, Any]) -> bool:
        """简单的重复判断"""
        # 1. 检查基本属性
        if entity1.get('type') != entity2.get('type'):
            return False

        if entity1.get('layer') != entity2.get('layer'):
            return False

        # 2. 检查坐标信息（简单比较）
        if 'points' in entity1 and 'points' in entity2:
            points1 = entity1['points']
            points2 = entity2['points']
            if len(points1) != len(points2):
                return False

            # 简单的坐标比较（允许小的浮点数误差）
            for p1, p2 in zip(points1, points2):
                if isinstance(p1, (list, tuple)) and isinstance(p2, (list, tuple)):
                    if len(p1) >= 2 and len(p2) >= 2:
                        if abs(p1[0] - p2[0]) > 0.01 or abs(p1[1] - p2[1]) > 0.01:
                            return False

        # 3. 检查起点终点（线条实体）
        if 'start_point' in entity1 and 'start_point' in entity2:
            start1, start2 = entity1['start_point'], entity2['start_point']
            end1, end2 = entity1.get('end_point'), entity2.get('end_point')

            if isinstance(start1, (list, tuple)) and isinstance(start2, (list, tuple)):
                if len(start1) >= 2 and len(start2) >= 2:
                    if abs(start1[0] - start2[0]) > 0.01 or abs(start1[1] - start2[1]) > 0.01:
                        return False

            if end1 and end2 and isinstance(end1, (list, tuple)) and isinstance(end2, (list, tuple)):
                if len(end1) >= 2 and len(end2) >= 2:
                    if abs(end1[0] - end2[0]) > 0.01 or abs(end1[1] - end2[1]) > 0.01:
                        return False

        # 4. 检查文字内容
        if 'text' in entity1 and 'text' in entity2:
            if entity1['text'] != entity2['text']:
                return False

        # 5. 检查颜色（可选）
        color1, color2 = entity1.get('color'), entity2.get('color')
        if color1 is not None and color2 is not None:
            if color1 != color2:
                return False

        # 如果所有检查都通过，认为是重复实体
        return True

    def _prepare_final_output(self, processed_entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """准备最终输出数据"""
        print(f"   📤 准备最终输出数据...")

        # 添加最终输出标记
        final_entities = []
        for entity in processed_entities:
            entity_copy = copy.deepcopy(entity)
            entity_copy['processing_stage'] = 'final_output'
            entity_copy['output_timestamp'] = time.time()
            entity_copy['processed_by_system'] = 'IndependentLayerProcessor'
            final_entities.append(entity_copy)

        print(f"     ✅ 最终输出准备完成: {len(final_entities)} 个实体")

        return final_entities

    def _is_line_entity(self, entity: Dict[str, Any]) -> bool:
        """判断是否为线条实体"""
        entity_type = entity.get('type', '').upper()
        return entity_type in ['LINE', 'LWPOLYLINE', 'POLYLINE']

    def _extract_line_coordinates(self, entity: Dict[str, Any]) -> List[List[float]]:
        """提取线条坐标"""
        if 'points' in entity:
            return entity['points']
        elif 'start_point' in entity and 'end_point' in entity:
            return [entity['start_point'], entity['end_point']]
        return None

    def get_processing_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return {
            'processing_stats': self.processing_stats,
            'wall_layer_patterns': self.wall_layer_patterns,
            'processing_strategy': {
                'wall_layers': 'iterative_merge',
                'other_layers': 'simple_deduplication',
                'layer_independence': True
            }
        }

    def get_layer_processing_report(self) -> Dict[str, Any]:
        """获取图层处理报告"""
        report = {
            'summary': {
                'total_layers': self.processing_stats['total_layers_processed'],
                'wall_layers': self.processing_stats['wall_layers_count'],
                'other_layers': self.processing_stats['other_layers_count'],
                'total_input_entities': self.processing_stats['total_entities_input'],
                'total_output_entities': self.processing_stats['total_entities_output'],
                'total_processing_time': self.processing_stats['processing_time'],
                'overall_reduction_rate': (
                    (self.processing_stats['total_entities_input'] - self.processing_stats['total_entities_output'])
                    / self.processing_stats['total_entities_input']
                ) if self.processing_stats['total_entities_input'] > 0 else 0
            },
            'processing_order': self.processing_stats['layer_processing_order'],
            'layer_details': dict(self.processing_stats['layer_details'])
        }

        return report

    def validate_processing_result(self, original_entities: List[Dict[str, Any]],
                                 processed_entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证处理结果"""
        validation = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'layer_integrity': {},
            'processing_integrity': {}
        }

        # 检查图层信息保留
        original_layers = set()
        processed_layers = set()

        for entity in original_entities:
            if isinstance(entity, dict) and 'layer' in entity:
                original_layers.add(entity['layer'])

        for entity in processed_entities:
            if isinstance(entity, dict) and 'layer' in entity:
                processed_layers.add(entity['layer'])

        # 验证图层完整性
        missing_layers = original_layers - processed_layers
        if missing_layers:
            validation['errors'].append(f"丢失图层: {missing_layers}")
            validation['is_valid'] = False

        validation['layer_integrity'] = {
            'original_layers': len(original_layers),
            'processed_layers': len(processed_layers),
            'missing_layers': list(missing_layers),
            'preservation_rate': len(processed_layers) / len(original_layers) if original_layers else 1.0
        }

        # 验证处理完整性
        processing_stages = set()
        layer_processing_types = set()

        for entity in processed_entities:
            if isinstance(entity, dict):
                if 'processing_stage' in entity:
                    processing_stages.add(entity['processing_stage'])
                if 'layer_processing_type' in entity:
                    layer_processing_types.add(entity['layer_processing_type'])

        validation['processing_integrity'] = {
            'processing_stages': list(processing_stages),
            'layer_processing_types': list(layer_processing_types),
            'all_entities_processed': 'final_output' in processing_stages,
            'processing_types_present': len(layer_processing_types) > 0
        }

        # 统计信息
        validation['statistics'] = {
            'original_entities': len(original_entities),
            'processed_entities': len(processed_entities),
            'reduction_count': len(original_entities) - len(processed_entities),
            'reduction_rate': (len(original_entities) - len(processed_entities)) / len(original_entities) if original_entities else 0
        }

        return validation


# 工厂函数
def create_independent_processor(distance_threshold=5, angle_threshold=2) -> IndependentLayerProcessor:
    """创建独立图层处理器"""
    return IndependentLayerProcessor(distance_threshold, angle_threshold)
