#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
可视化性能修复方案
针对发现的可视化瓶颈问题提供解决方案
"""

import time
from typing import List, Dict, Any, Optional


class VisualizationPerformanceFix:
    """
    可视化性能修复器

    解决可视化时间过长的问题
    """

    def __init__(self):
        """初始化性能修复器"""
        self.entity_cache = {}
        self.last_entity_hash = None
        self.skip_redundant_updates = True
        
    def should_skip_update(self, entities: List[Dict[str, Any]]) -> bool:
        """判断是否应该跳过冗余更新"""
        if not self.skip_redundant_updates:
            return False
        
        # 计算实体列表的简单哈希
        entity_hash = hash(str(len(entities)) + str(id(entities[0]) if entities else ""))
        
        if entity_hash == self.last_entity_hash:
            print("⚡ 跳过冗余可视化更新")
            return True
        
        self.last_entity_hash = entity_hash
        return False
    
    def optimize_draw_entities(self, visualizer, entities: List[Dict[str, Any]]) -> bool:
        """优化实体绘制"""
        if not entities:
            return True

        # 检查是否跳过冗余更新
        if self.should_skip_update(entities):
            return True

        entity_count = len(entities)
        print(f"⚡ 使用批量绘制模式({entity_count}个实体)")
        return self._batch_draw(visualizer, entities)
    
    def _batch_draw(self, visualizer, entities: List[Dict[str, Any]]) -> bool:
        """批量绘制模式"""
        try:
            if hasattr(visualizer, 'ax_detail'):
                ax = visualizer.ax_detail
                ax.clear()
                ax.set_aspect('equal')
                ax.grid(True, linestyle='--', alpha=0.3)

                # 按图层分组绘制
                layer_groups = {}
                for entity in entities:
                    layer = entity.get('layer', 'UNKNOWN')
                    if layer not in layer_groups:
                        layer_groups[layer] = []
                    layer_groups[layer].append(entity)

                # 为每个图层批量绘制
                colors = {'A-WALL': 'blue', 'A-WINDOW': 'red', '0': 'green'}

                for layer, layer_entities in layer_groups.items():
                    color = colors.get(layer, 'gray')

                    # 批量收集该图层的坐标
                    all_x, all_y = [], []
                    for entity in layer_entities:
                        if entity.get('type') == 'LINE' and 'points' in entity:
                            points = entity['points']
                            if len(points) >= 2:
                                x_coords = [points[0][0], points[-1][0]]
                                y_coords = [points[0][1], points[-1][1]]
                                all_x.extend(x_coords + [None])
                                all_y.extend(y_coords + [None])

                    # 一次性绘制该图层
                    if all_x and all_y:
                        ax.plot(all_x, all_y, color=color, linewidth=0.5, alpha=0.7, label=layer)

                ax.legend(fontsize=8)
                ax.set_title(f'批量绘制 ({len(entities)}个实体)', fontsize=10)

            print(f"✅ 批量绘制完成")
            return True

        except Exception as e:
            print(f"❌ 批量绘制失败: {e}")
            return False
    
    def optimize_visualize_overview(self, visualizer, all_entities: List[Dict[str, Any]],
                                  current_group_entities: Optional[List[Dict[str, Any]]] = None,
                                  labeled_entities: Optional[List[Dict[str, Any]]] = None,
                                  **kwargs) -> bool:
        """优化概览可视化"""
        if not all_entities:
            return True

        entity_count = len(all_entities)
        print(f"⚡ 使用批量概览模式({entity_count}个实体)")
        return self._batch_overview(visualizer, all_entities, current_group_entities, labeled_entities)
    
    def _batch_overview(self, visualizer, all_entities: List[Dict[str, Any]],
                      current_group_entities: Optional[List[Dict[str, Any]]] = None,
                      labeled_entities: Optional[List[Dict[str, Any]]] = None) -> bool:
        """批量概览模式"""
        try:
            if hasattr(visualizer, 'ax_overview'):
                ax = visualizer.ax_overview
                ax.clear()
                ax.set_aspect('equal')
                ax.grid(True, linestyle='--', alpha=0.3)

                # 使用ID集合进行快速查找
                current_ids = {id(e) for e in current_group_entities} if current_group_entities else set()
                labeled_ids = {id(e) for e in labeled_entities} if labeled_entities else set()

                # 分类收集坐标
                normal_x, normal_y = [], []
                current_x, current_y = [], []
                labeled_x, labeled_y = [], []

                for entity in all_entities:
                    if entity.get('type') == 'LINE' and 'points' in entity:
                        points = entity['points']
                        if len(points) >= 2:
                            x_coords = [points[0][0], points[-1][0]]
                            y_coords = [points[0][1], points[-1][1]]

                            entity_id = id(entity)
                            if entity_id in current_ids:
                                current_x.extend(x_coords + [None])
                                current_y.extend(y_coords + [None])
                            elif entity_id in labeled_ids:
                                labeled_x.extend(x_coords + [None])
                                labeled_y.extend(y_coords + [None])
                            else:
                                normal_x.extend(x_coords + [None])
                                normal_y.extend(y_coords + [None])

                # 批量绘制
                if normal_x and normal_y:
                    ax.plot(normal_x, normal_y, color='lightgray', linewidth=0.3, alpha=0.6)
                if labeled_x and labeled_y:
                    ax.plot(labeled_x, labeled_y, color='green', linewidth=0.6, alpha=0.8)
                if current_x and current_y:
                    ax.plot(current_x, current_y, color='red', linewidth=0.8, alpha=0.9)

                current_count = len(current_group_entities) if current_group_entities else 0
                labeled_count = len(labeled_entities) if labeled_entities else 0
                ax.set_title(f'批量概览 (总:{len(all_entities)}, 当前:{current_count}, 已标注:{labeled_count})', fontsize=10)

            print(f"✅ 批量概览完成")
            return True

        except Exception as e:
            print(f"❌ 批量概览失败: {e}")
            return False
    
    def get_performance_settings(self) -> Dict[str, Any]:
        """获取性能设置"""
        return {
            'skip_redundant_updates': self.skip_redundant_updates,
            'optimization_strategy': 'batch_drawing + id_lookup + redundancy_skip'
        }


def apply_visualization_performance_fix(visualizer):
    """为可视化器应用性能修复"""
    fix = VisualizationPerformanceFix()
    
    # 保存原始方法
    original_draw_entities = visualizer.draw_entities
    original_visualize_overview = visualizer.visualize_overview
    
    def optimized_draw_entities(entities):
        """优化的绘制实体方法"""
        start_time = time.time()
        
        # 尝试使用优化绘制
        if fix.optimize_draw_entities(visualizer, entities):
            print(f"⚡ 优化绘制完成: {time.time() - start_time:.3f}秒")
            return
        
        # 回退到原始方法
        print(f"🔄 使用原始绘制方法")
        original_draw_entities(entities)
    
    def optimized_visualize_overview(all_entities, current_group_entities=None, 
                                   labeled_entities=None, **kwargs):
        """优化的概览可视化方法"""
        start_time = time.time()
        
        # 尝试使用优化概览
        if fix.optimize_visualize_overview(visualizer, all_entities, 
                                         current_group_entities, labeled_entities, **kwargs):
            print(f"⚡ 优化概览完成: {time.time() - start_time:.3f}秒")
            return
        
        # 回退到原始方法
        print(f"🔄 使用原始概览方法")
        original_visualize_overview(all_entities, current_group_entities, labeled_entities, **kwargs)
    
    # 替换方法
    visualizer.draw_entities = optimized_draw_entities
    visualizer.visualize_overview = optimized_visualize_overview
    
    # 添加性能修复标记
    visualizer._performance_fix_applied = True
    visualizer._performance_fix = fix
    
    print("⚡ 可视化性能修复已应用")
    print(f"   设置: {fix.get_performance_settings()}")


# 全局修复实例
_visualization_performance_fix = None


def get_visualization_performance_fix():
    """获取可视化性能修复实例"""
    global _visualization_performance_fix
    if _visualization_performance_fix is None:
        _visualization_performance_fix = VisualizationPerformanceFix()
    return _visualization_performance_fix
